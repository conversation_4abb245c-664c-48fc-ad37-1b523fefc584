<?php
use yii\helpers\Html;
$calculatedTotalSum = 0;
?>

<div class="invoice-details">
    <!-- Кнопка печатать накладную -->
    <div class="text-right mb-1">
        <button type="button" class="btn btn-primary print-invoice-btn" data-id="<?= $model->id ?>">
            <i class="fas fa-print"></i> <?= Yii::t('app', 'print_invoice') ?>
        </button>
    </div>

    <div class="card-body">
        <h6 style="margin-bottom: -0.5rem;"><?= Yii::t('app', 'Invoice Information') ?></h6>

        <table class="table table-bordered table-hover">
            <thead>
                <tr>
                    <th><?= Yii::t('app', 'client') ?></th>
                    <th><?= Yii::t('app', 'driver') ?></th>
                    <th><?= Yii::t('app', 'car_number') ?></th>
                    <th><?= Yii::t('app', 'created_at') ?></th>
                    <th><?= Yii::t('app', 'sell_user') ?></th>
                    <?php if ($model->confirm_user_id): ?>
                        <th><?= Yii::t('app', 'confirm_user') ?></th>
                    <?php endif; ?>
                    <th><?= Yii::t('app', 'status') ?></th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><?= Html::encode($model->client->full_name) ?></td>
                    <td><?= Html::encode($model->driver) ?></td>
                    <td><?= Html::encode($model->car_number) ?></td>
                    <td><?= date("d.m.Y", strtotime($model->created_at)) ?></td>
                    <td><?= Html::encode($model->sellUser->username) ?></td>
                    <?php if ($model->confirm_user_id): ?>
                        <td><?= Html::encode($model->confirmUser->username) ?></td>
                    <?php endif; ?>
                    <td>
                        <span class="<?= $model->deleted_at == NULL ? 'badge badge-outline-success' : 'badge badge-danger' ?>">
                            <?= $model->deleted_at == NULL ? Yii::t("app", "active") : Yii::t("app", "inactive") ?>
                        </span>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- Детали продуктов -->
    <div class="card-body">
        <h6 style="margin-bottom: -0.5rem;"><?= Yii::t('app', 'products') ?></h6>

        <div class="table-responsive">
            <table class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th>#</th>
                        <th><?= Yii::t('app', 'product') ?></th>
                        <th><?= Yii::t('app', 'quantity') ?></th>
                        <th><?= Yii::t('app', 'price') ?></th>
                        <th><?= Yii::t('app', 'all') ?></th>
                        <th><?= Yii::t('app', 'status') ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($model->salesDetails as $index => $detail): ?>
                        <tr>
                            <td><?= $index + 1 ?></td>
                            <td><?= Html::encode($detail->product->name) ?></td>
                            <td><?= Html::encode($detail->quantity) ?></td>
                            <td><?= number_format($detail->special_price, 0, '.', ' ') ?></td>
                            <td><?= number_format($detail->quantity * $detail->special_price, 0, '.', ' ') ?></td>
                            <td>
                                <span class="<?= $detail->deleted_at == NULL ? 'badge badge-outline-success' : 'badge badge-danger' ?>">
                                    <?= $detail->deleted_at == NULL ? Yii::t("app", "active") : Yii::t("app", "inactive") ?>
                                </span>
                            </td>
                        </tr>
                          <?php
                            if ($detail->deleted_at == NULL) {
                                $quantity = is_numeric($detail->quantity) ? $detail->quantity : 0;
                                $price = is_numeric($detail->special_price) ? $detail->special_price : 0;
                                $calculatedTotalSum += $quantity * $price;
                            }
                            ?>
                    <?php endforeach; ?>

                    <?php if (!empty($model->bonus)): ?>
                        <tr>
                            <td colspan="6" class="bg-light"><strong><?= Yii::t('app', 'bonus_products') ?></strong></td>
                        </tr>
                        <?php foreach ($model->bonus as $bonus): ?>
                            <tr class="bonus-row">
                                <td colspan="2"><?= $bonus->product->name ?></td>
                                <td><?= $bonus->quantity ?></td>
                                <td colspan="3"></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="4" class="text-right"><strong><?= Yii::t('app', 'total_sum') ?>:</strong></td>
                        <td colspan="2"><strong><?= number_format($calculatedTotalSum, 0, '.', ' ') ?></strong></td>
                    </tr>
                </tfoot>
            </table>
        </div>




    </div>
</div>

<script>
$(document).ready(function() {
    // Обработчик клика по кнопке печати накладной
    $(document).off('click.print-invoice-btn').on('click.print-invoice-btn', '.print-invoice-btn', function() {
        var invoiceId = $(this).data('id');
        
        $.ajax({
            url: '/backend/print-invoice/view',
            type: 'GET',
            data: { id: invoiceId },
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    // Показываем модальное окно с возможностью редактировать номер накладной
                    $('#ideal-large-modal-without-save .modal-title').html('<?= Yii::t('app', 'print_invoice') ?>');
                    $('#ideal-large-modal-without-save .modal-body').html(response.content);
                    $('#ideal-large-modal-without-save').modal('show');
                } else {
                    if (typeof iziToast !== 'undefined') {
                        iziToast.error({
                            title: 'Ошибка',
                            message: response.message || 'Произошла ошибка при загрузке печатной накладной',
                            position: 'topRight'
                        });
                    } else {
                        alert(response.message || 'Произошла ошибка при загрузке печатной накладной');
                    }
                }
            },
            error: function(xhr, textStatus, errorThrown) {
                console.error('Ошибка AJAX:', xhr.statusText, errorThrown);
                if (typeof iziToast !== 'undefined') {
                    iziToast.error({
                        title: 'Ошибка',
                        message: 'Не удалось загрузить печатную накладную',
                        position: 'topRight'
                    });
                } else {
                    alert('Не удалось загрузить печатную накладную');
                }
            }
        });
    });

    // Функция глобально доступная для печати накладной из модалки
    window.printInvoice = function () {
        // Проверяем, что номер накладной введён
        var invoiceNumber = $('#ideal-large-modal-without-save #print-number').val();
        if (!invoiceNumber || invoiceNumber == '0' || invoiceNumber.trim() === '') {
            alert('Пожалуйста, введите номер накладной перед печатью!');
            $('#ideal-large-modal-without-save #print-number').focus();
            return;
        }
        
        // Получаем HTML контент и заменяем значение номера
        var printHTML = $('#ideal-large-modal-without-save .print-invoice-view').prop('outerHTML');
        // Заменяем value="0" на введенный номер
        printHTML = printHTML.replace(/value="[^"]*"/, 'value="' + invoiceNumber + '"');
        
        var printWindow = window.open('', '_blank', 'width=800,height=600');
        printWindow.document.write(`<!DOCTYPE html><html><head><meta charset="utf-8"><title>Invoice</title></head><body>${printHTML}</body></html>`);
        printWindow.document.close();
        printWindow.onload = function() {
            printWindow.print();
            printWindow.close();
        };
    };
});
</script>
